'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, TrendingUp, <PERSON><PERSON>, <PERSON>bulb, ArrowRight } from 'lucide-react';
import { motion } from 'framer-motion';
import { toast } from 'sonner';

interface HookBoosterProps {
  hookDNA: any;
}

const mockImprovedHooks = [
  {
    original: "I learned something interesting about productivity today.",
    improved: "What if I told you that 99% of productivity advice is actually making you LESS productive?",
    originalScore: 23,
    improvedScore: 89,
    improvements: [
      "Added question hook pattern (your strongest style)",
      "Introduced contrarian angle to create curiosity gap",
      "Used specific percentage for authority",
      "Created immediate value promise"
    ]
  }
];

export function HookBooster({ hookDNA }: HookBoosterProps) {
  const [originalHook, setOriginalHook] = useState('');
  const [improvedVersions, setImprovedVersions] = useState<any[]>([]);
  const [isBoosting, setIsBoosting] = useState(false);

  const handleBoost = async () => {
    if (!originalHook.trim()) {
      toast.error('Please enter a hook to improve');
      return;
    }

    setIsBoosting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Generate 3 improved versions
      const improvedVersionsData = [
        {
          improved: "What if I told you that 99% of productivity advice is actually making you LESS productive?",
          score: 89,
          improvements: [
            "Added question hook pattern (your strongest style)",
            "Introduced contrarian angle to create curiosity gap", 
            "Used specific percentage for credibility",
          ]
        },
        {
          improved: "The productivity mistake that's costing you 3 hours per day (and how I fixed it)",
          score: 82,
          improvements: [
            "Specific problem + solution format",
            "Added personal story element",
            "Quantified the benefit (3 hours)",
          ]
        },
        {
          improved: "I spent $10K on productivity courses. Here's the only thing that actually worked:",
          score: 78,
          improvements: [
            "Personal investment story creates authority",
            "Promise of filtered, tested advice",
            "Sets up expectation for valuable insight",
          ]
        }
      ];

      setImprovedVersions(improvedVersionsData.map(version => ({
        ...version,
        original: originalHook,
        originalScore: 25 // Mock score for original
      })));
      
      toast.success('Hook improvements generated!');
    } catch (error) {
      toast.error('Failed to generate improvements');
    } finally {
      setIsBoosting(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard!');
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-3">
          Boost Your Hooks
        </h2>
        <p className="text-lg text-gray-600">
          Transform any hook into a viral magnet using your unique hook DNA patterns.
        </p>
      </div>

      {/* Input Section */}
      <Card className="p-8">
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Enter your hook to improve *
          </label>
          <Textarea
            placeholder="e.g., I learned something interesting about productivity today."
            value={originalHook}
            onChange={(e) => setOriginalHook(e.target.value)}
            rows={3}
            className="text-lg"
          />
          
          <Button 
            onClick={handleBoost}
            disabled={isBoosting || !originalHook.trim()}
            size="lg"
            className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
          >
            {isBoosting ? (
              <>
                <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                Analyzing & Improving...
              </>
            ) : (
              <>
                <TrendingUp className="w-5 h-5 mr-2" />
                Boost This Hook
              </>
            )}
          </Button>
        </div>
      </Card>

      {/* Improved Versions */}
      {improvedVersions.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="space-y-6"
        >
          <h3 className="text-2xl font-bold text-gray-900">Improved Versions</h3>

          {/* Original vs First Improved Comparison */}
          <Card className="p-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              {/* Original */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Badge variant="outline" className="text-red-600 border-red-200">
                    Original
                  </Badge>
                  <div className="flex items-center text-red-600">
                    <div className="w-3 h-3 bg-red-400 rounded-full mr-2"></div>
                    <span className="text-sm font-medium">Low Performance</span>
                  </div>
                </div>
                
                <blockquote className="text-lg text-gray-700 italic p-4 bg-red-50 rounded-lg border-l-4 border-red-300">
                  "{originalHook}"
                </blockquote>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Viralness Score</span>
                  <div className="flex items-center">
                    <div className="w-20 h-2 bg-gray-200 rounded-full mr-3">
                      <div className="w-1/4 h-2 bg-red-400 rounded-full"></div>
                    </div>
                    <span className="text-lg font-bold text-red-600">25</span>
                  </div>
                </div>
              </div>

              {/* Arrow */}
              <div className="flex justify-center">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center">
                  <ArrowRight className="w-6 h-6 text-white" />
                </div>
              </div>

              {/* Best Improved Version */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Badge className="bg-green-600 text-white">
                    Best Version
                  </Badge>
                  <div className="flex items-center text-green-600">
                    <TrendingUp className="w-4 h-4 mr-1" />
                    <span className="text-sm font-medium">High Performance</span>
                  </div>
                </div>
                
                <blockquote className="text-lg text-gray-900 font-medium p-4 bg-green-50 rounded-lg border-l-4 border-green-500">
                  "{improvedVersions[0].improved}"
                </blockquote>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Viralness Score</span>
                  <div className="flex items-center">
                    <div className="w-20 h-2 bg-gray-200 rounded-full mr-3">
                      <div className="w-full h-2 bg-green-500 rounded-full"></div>
                    </div>
                    <span className="text-lg font-bold text-green-600">{improvedVersions[0].score}</span>
                  </div>
                </div>

                <Button
                  onClick={() => copyToClipboard(improvedVersions[0].improved)}
                  variant="outline"
                  size="sm"
                  className="w-full"
                >
                  <Copy className="w-4 h-4 mr-2" />
                  Copy This Version
                </Button>
              </div>
            </div>
          </Card>

          {/* All Improved Versions */}
          <div className="space-y-4">
            <h4 className="text-xl font-semibold text-gray-900">All Improved Versions</h4>
            {improvedVersions.map((version, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <Card className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <blockquote className="text-lg text-gray-900 font-medium mb-3">
                        "{version.improved}"
                      </blockquote>
                      
                      <Badge 
                        className={`mb-3 ${
                          version.score >= 80 
                            ? 'bg-green-100 text-green-800' 
                            : version.score >= 70 
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-orange-100 text-orange-800'
                        }`}
                      >
                        Viralness Score: {version.score} (+{version.score - 25})
                      </Badge>

                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                          <Lightbulb className="w-4 h-4 text-yellow-600" />
                          Key Improvements:
                        </div>
                        <ul className="text-sm text-gray-600 space-y-1">
                          {version.improvements.map((improvement: string, i: number) => (
                            <li key={i} className="flex items-start gap-2">
                              <span className="text-green-600 mt-1">•</span>
                              <span>{improvement}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>

                    <Button
                      onClick={() => copyToClipboard(version.improved)}
                      variant="outline"
                      size="sm"
                      className="ml-4"
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>

          <div className="bg-purple-50 p-6 rounded-lg border border-purple-200">
            <h4 className="font-semibold text-purple-900 mb-2">Based on Your Hook DNA:</h4>
            <ul className="text-sm text-purple-800 space-y-1">
              <li>• Questions are your strongest hook pattern (85% success rate)</li>
              <li>• Contrarian angles increase your engagement by 3.2x</li>
              <li>• Specific numbers and percentages boost credibility</li>
              <li>• Personal stories perform 2.1x better than generic advice</li>
            </ul>
          </div>
        </motion.div>
      )}
    </div>
  );
}