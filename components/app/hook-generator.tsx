'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON>ader2, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, RefreshCw, Lightbulb } from 'lucide-react';
import { motion } from 'framer-motion';
import { toast } from 'sonner';

interface HookGeneratorProps {
  hookDNA: any;
}

const mockGeneratedHooks = [
  {
    text: "What if I told you that 99% of productivity advice is actually making you LESS productive?",
    score: 89,
    pattern: "Question + Contrarian",
    reason: "Combines your top hook pattern (questions) with contrarian angle that drives curiosity"
  },
  {
    text: "The productivity mistake that's costing founders $50K per year (and how to fix it in 10 minutes)",
    score: 82,
    pattern: "Problem + Solution",
    reason: "Uses specific numbers and time frames that perform well in your content"
  },
  {
    text: "I spent $25K on productivity courses. Here's the only advice that actually worked:",
    score: 78,
    pattern: "Story + List",
    reason: "Personal story opener with promise of actionable insights - your audience loves this"
  },
  {
    text: "Stop doing these 5 productivity hacks. They're destroying your focus (thread)",
    score: 75,
    pattern: "Command + List + Thread",
    reason: "Direct command with numbered list - matches your high-performing tweet structure"
  },
  {
    text: "The productivity secret that billionaires don't want you to know:",
    score: 73,
    pattern: "Secret + Authority",
    reason: "Appeals to curiosity gap and authority bias - two elements strong in your DNA"
  },
];

export function HookGenerator({ hookDNA }: HookGeneratorProps) {
  const [topic, setTopic] = useState('');
  const [customPrompt, setCustomPrompt] = useState('');
  const [generatedHooks, setGeneratedHooks] = useState<any[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  const handleGenerate = async () => {
    if (!topic.trim()) {
      toast.error('Please enter a topic or idea');
      return;
    }

    setIsGenerating(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      setGeneratedHooks(mockGeneratedHooks);
      toast.success('Hooks generated successfully!');
    } catch (error) {
      toast.error('Failed to generate hooks');
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard!');
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-3">
          Generate Viral Hooks
        </h2>
        <p className="text-lg text-gray-600">
          Create hooks that match your unique style and maximize engagement potential.
        </p>
      </div>

      {/* Input Section */}
      <Card className="p-8">
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Topic or Main Idea *
            </label>
            <Input
              placeholder="e.g., productivity tips for entrepreneurs"
              value={topic}
              onChange={(e) => setTopic(e.target.value)}
              className="text-lg"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Additional Context (Optional)
            </label>
            <Textarea
              placeholder="Add any specific angle, target audience, or key points you want to include..."
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
              rows={3}
            />
          </div>

          <Button 
            onClick={handleGenerate}
            disabled={isGenerating || !topic.trim()}
            size="lg"
            className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
          >
            {isGenerating ? (
              <>
                <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                Generating Hooks...
              </>
            ) : (
              <>
                <Sparkles className="w-5 h-5 mr-2" />
                Generate 5 Hooks
              </>
            )}
          </Button>
        </div>
      </Card>

      {/* Generated Hooks */}
      {generatedHooks.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="space-y-6"
        >
          <div className="flex items-center justify-between">
            <h3 className="text-2xl font-bold text-gray-900">Generated Hooks</h3>
            <Button 
              onClick={handleGenerate}
              variant="outline"
              disabled={isGenerating}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Generate New Set
            </Button>
          </div>

          <div className="grid gap-4">
            {generatedHooks.map((hook, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <Card className="p-6 hover:shadow-lg transition-shadow duration-200">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <blockquote className="text-lg text-gray-900 font-medium mb-3">
                        "{hook.text}"
                      </blockquote>
                      
                      <div className="flex items-center gap-3 mb-3">
                        <Badge 
                          className={`${
                            hook.score >= 80 
                              ? 'bg-green-100 text-green-800' 
                              : hook.score >= 70 
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-orange-100 text-orange-800'
                          }`}
                        >
                          Viralness Score: {hook.score}
                        </Badge>
                        <Badge variant="outline">
                          {hook.pattern}
                        </Badge>
                      </div>

                      <div className="flex items-start gap-2 text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                        <Lightbulb className="w-4 h-4 mt-0.5 text-yellow-600 flex-shrink-0" />
                        <p><strong>Why this works:</strong> {hook.reason}</p>
                      </div>
                    </div>

                    <Button
                      onClick={() => copyToClipboard(hook.text)}
                      variant="outline"
                      size="sm"
                      className="ml-4"
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>

          <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
            <h4 className="font-semibold text-blue-900 mb-2">Pro Tips:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Test multiple hooks for the same content to find your winner</li>
              <li>• Higher viralness scores indicate better potential engagement</li>
              <li>• Combine your top patterns for maximum impact</li>
              <li>• Post during your optimal times (6PM shows highest engagement)</li>
            </ul>
          </div>
        </motion.div>
      )}
    </div>
  );
}