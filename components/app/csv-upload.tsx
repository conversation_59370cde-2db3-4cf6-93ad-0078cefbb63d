'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import <PERSON> from 'papaparse';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Upload, FileText, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { motion } from 'framer-motion';

interface CSVUploadProps {
  onUpload: (data: any[]) => void;
  isAnalyzing: boolean;
}

export function CSVUpload({ onUpload, isAnalyzing }: CSVUploadProps) {
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'processing' | 'success' | 'error'>('idle');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [rowCount, setRowCount] = useState(0);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    setUploadedFile(file);
    setUploadStatus('processing');

    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        if (results.errors.length > 0) {
          setUploadStatus('error');
          return;
        }

        const tweets = results.data.filter((row: any) => 
          row.text || row.tweet || row.content
        );

        setRowCount(tweets.length);
        setUploadStatus('success');
        onUpload(tweets);
      },
      error: () => {
        setUploadStatus('error');
      }
    });
  }, [onUpload]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.ms-excel': ['.csv'],
    },
    multiple: false,
  });

  return (
    <div className="max-w-2xl mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-3">
          Upload Your Twitter Data
        </h2>
        <p className="text-gray-600">
          Upload your Twitter CSV export to analyze your hook patterns and performance.
        </p>
      </div>

      {uploadStatus === 'idle' && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <Card
            {...getRootProps()}
            className={`p-12 border-2 border-dashed cursor-pointer transition-all duration-200 ${
              isDragActive 
                ? 'border-purple-400 bg-purple-50' 
                : 'border-gray-300 hover:border-purple-300 hover:bg-gray-50'
            }`}
          >
            <input {...getInputProps()} />
            <div className="text-center">
              <Upload className={`w-16 h-16 mx-auto mb-4 ${
                isDragActive ? 'text-purple-600' : 'text-gray-400'
              }`} />
              {isDragActive ? (
                <p className="text-lg font-medium text-purple-600 mb-2">
                  Drop your CSV file here...
                </p>
              ) : (
                <>
                  <p className="text-lg font-medium text-gray-900 mb-2">
                    Drag & drop your CSV file here
                  </p>
                  <p className="text-gray-500 mb-4">
                    or click to select from your computer
                  </p>
                </>
              )}
              <Button variant="outline" className="mt-4">
                <FileText className="w-4 h-4 mr-2" />
                Select CSV File
              </Button>
            </div>
          </Card>
        </motion.div>
      )}

      {uploadStatus === 'processing' && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="p-12 text-center">
            <Loader2 className="w-16 h-16 mx-auto mb-4 text-purple-600 animate-spin" />
            <p className="text-lg font-medium text-gray-900 mb-2">
              Processing your CSV file...
            </p>
            <p className="text-gray-500">
              Parsing tweets and extracting engagement data
            </p>
          </Card>
        </motion.div>
      )}

      {uploadStatus === 'success' && !isAnalyzing && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="p-8 border-green-200 bg-green-50/50">
            <div className="flex items-center mb-4">
              <CheckCircle className="w-8 h-8 text-green-600 mr-3" />
              <div>
                <p className="text-lg font-medium text-green-900">
                  File uploaded successfully!
                </p>
                <p className="text-green-700">
                  {uploadedFile?.name} • {rowCount.toLocaleString()} tweets processed
                </p>
              </div>
            </div>
            <div className="mt-6 p-4 bg-white rounded-lg border border-green-200">
              <p className="text-sm text-gray-600 mb-2">
                <strong>Next:</strong> We'll analyze your hook patterns, identify your top-performing styles, 
                and create your unique Hook DNA profile.
              </p>
            </div>
          </Card>
        </motion.div>
      )}

      {uploadStatus === 'success' && isAnalyzing && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="p-12 text-center">
            <div className="relative mb-6">
              <Loader2 className="w-16 h-16 mx-auto text-purple-600 animate-spin" />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-8 h-8 bg-purple-100 rounded-full animate-pulse"></div>
              </div>
            </div>
            <p className="text-lg font-medium text-gray-900 mb-2">
              Analyzing your hook DNA...
            </p>
            <p className="text-gray-500 mb-4">
              Our AI is studying your {rowCount.toLocaleString()} tweets to identify patterns
            </p>
            <div className="w-full bg-gray-200 rounded-full h-2 max-w-xs mx-auto">
              <div className="bg-purple-600 h-2 rounded-full animate-pulse" style={{ width: '75%' }}></div>
            </div>
          </Card>
        </motion.div>
      )}

      {uploadStatus === 'error' && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="p-8 border-red-200 bg-red-50/50">
            <div className="flex items-center mb-4">
              <AlertCircle className="w-8 h-8 text-red-600 mr-3" />
              <div>
                <p className="text-lg font-medium text-red-900">
                  Upload failed
                </p>
                <p className="text-red-700">
                  Please check your CSV format and try again
                </p>
              </div>
            </div>
            <Button 
              onClick={() => setUploadStatus('idle')}
              variant="outline"
              className="mt-4"
            >
              Try Again
            </Button>
          </Card>
        </motion.div>
      )}

      <div className="mt-8 text-center text-sm text-gray-500">
        <p className="mb-2">
          <strong>CSV Format:</strong> Include columns for tweet text and engagement metrics (likes, retweets, replies)
        </p>
        <p>
          Don't have your Twitter data? <a href="https://help.twitter.com/en/managing-your-account/how-to-download-your-twitter-archive" className="text-purple-600 hover:underline" target="_blank" rel="noopener noreferrer">Learn how to download it</a>
        </p>
      </div>
    </div>
  );
}