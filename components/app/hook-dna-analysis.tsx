'use client';

import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'recharts';
import { TrendingUp, Target, Lightbulb, Award } from 'lucide-react';
import { motion } from 'framer-motion';

interface HookDNAAnalysisProps {
  data: any;
}

const mockData = {
  hookPatterns: [
    { name: 'Question Hooks', percentage: 85, color: '#8B5CF6', engagement: 12.3 },
    { name: 'Contrarian Takes', percentage: 72, color: '#3B82F6', engagement: 9.8 },
    { name: 'Story Starters', percentage: 63, color: '#10B981', engagement: 8.2 },
    { name: 'List Formats', percentage: 45, color: '#F59E0B', engagement: 6.1 },
    { name: 'Behind Scenes', percentage: 38, color: '#EF4444', engagement: 5.4 },
  ],
  topWords: [
    'productivity', 'marketing', 'startup', 'founder', 'growth', 
    'mistake', 'secret', 'truth', 'reality', 'actually'
  ],
  engagementByTime: [
    { hour: '6AM', engagement: 3.2 },
    { hour: '9AM', engagement: 8.1 },
    { hour: '12PM', engagement: 6.5 },
    { hour: '3PM', engagement: 9.2 },
    { hour: '6PM', engagement: 12.8 },
    { hour: '9PM', engagement: 7.3 },
  ],
  insights: [
    {
      icon: Target,
      title: 'Your Sweet Spot',
      description: 'Question hooks combined with contrarian takes generate 3x more engagement than average.',
    },
    {
      icon: TrendingUp,
      title: 'Peak Performance',
      description: 'Your best performing tweets average 847 engagements vs. your overall average of 234.',
    },
    {
      icon: Lightbulb,
      title: 'Untapped Opportunity',
      description: 'Story-driven hooks show high potential but represent only 15% of your content.',
    },
  ]
};

export function HookDNAAnalysis({ data }: HookDNAAnalysisProps) {
  const analysisData = data || mockData;

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-3">
          Your Hook DNA Analysis
        </h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          We've analyzed your content to identify your unique hook patterns and engagement drivers.
        </p>
      </div>

      {/* Hook Patterns */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Card className="p-8">
          <div className="flex items-center mb-6">
            <Award className="w-6 h-6 text-purple-600 mr-3" />
            <h3 className="text-2xl font-bold text-gray-900">Your Top Hook Patterns</h3>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="space-y-6">
              {analysisData.hookPatterns.map((pattern: any, index: number) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="flex items-center justify-between"
                >
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-gray-900">{pattern.name}</span>
                      <span className="text-sm text-gray-600">{pattern.percentage}%</span>
                    </div>
                    <Progress 
                      value={pattern.percentage} 
                      className="h-3"
                      style={{ 
                        '--progress-background': pattern.color 
                      } as React.CSSProperties}
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      Avg. {pattern.engagement}x engagement boost
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
            
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={analysisData.hookPatterns}
                    cx="50%"
                    cy="50%"
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="percentage"
                    label={({ name, percentage }) => `${name}: ${percentage}%`}
                  >
                    {analysisData.hookPatterns.map((entry: any, index: number) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
        </Card>
      </motion.div>

      {/* Top Words */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <Card className="p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">Your Power Words</h3>
          <div className="flex flex-wrap gap-3">
            {analysisData.topWords.map((word: string, index: number) => (
              <Badge 
                key={index} 
                variant="secondary" 
                className="px-4 py-2 text-sm font-medium bg-gradient-to-r from-purple-100 to-blue-100 text-purple-700 border-none"
              >
                {word}
              </Badge>
            ))}
          </div>
          <p className="text-gray-600 mt-4">
            These words appear frequently in your highest-performing hooks and drive strong engagement.
          </p>
        </Card>
      </motion.div>

      {/* Engagement Timeline */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <Card className="p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">Optimal Posting Times</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={analysisData.engagementByTime}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="hour" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="engagement" fill="#8B5CF6" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </Card>
      </motion.div>

      {/* Key Insights */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {analysisData.insights.map((insight: any, index: number) => (
            <Card key={index} className="p-6">
              <insight.icon className="w-8 h-8 text-purple-600 mb-4" />
              <h4 className="font-semibold text-gray-900 mb-2">{insight.title}</h4>
              <p className="text-gray-600 text-sm">{insight.description}</p>
            </Card>
          ))}
        </div>
      </motion.div>
    </div>
  );
}