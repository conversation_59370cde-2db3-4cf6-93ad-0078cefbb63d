'use client';

import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { ArrowRight, TrendingUp } from 'lucide-react';
import { motion } from 'framer-motion';

const beforeAfter = {
  before: {
    text: "I learned something interesting about productivity today.",
    score: 23,
    engagement: "12 likes, 3 retweets",
  },
  after: {
    text: "What if I told you that 99% of productivity advice is actually making you LESS productive?",
    score: 89,
    engagement: "1.2K likes, 340 retweets",
    pattern: "Question + Contrarian + Curiosity Gap",
  },
};

export function ExampleOutput() {
  return (
    <section id="examples" className="py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
            Before vs After
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed">
            See how HookForge transforms ordinary hooks into viral magnets using your unique style.
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            {/* Before */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <Card className="p-8 border-2 border-red-100 bg-red-50/30">
                <div className="flex items-center justify-between mb-6">
                  <Badge variant="outline" className="text-red-600 border-red-200">
                    Before
                  </Badge>
                  <div className="flex items-center text-red-600">
                    <div className="w-3 h-3 bg-red-400 rounded-full mr-2"></div>
                    <span className="text-sm font-medium">Low Performance</span>
                  </div>
                </div>
                
                <blockquote className="text-lg text-gray-700 mb-6 italic">
                  "{beforeAfter.before.text}"
                </blockquote>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Viralness Score</span>
                    <div className="flex items-center">
                      <div className="w-20 h-2 bg-gray-200 rounded-full mr-3">
                        <div 
                          className="h-2 bg-red-400 rounded-full transition-all duration-1000"
                          style={{ width: `${beforeAfter.before.score}%` }}
                        ></div>
                      </div>
                      <span className="text-lg font-bold text-red-600">{beforeAfter.before.score}</span>
                    </div>
                  </div>
                  
                  <div className="pt-4 border-t border-gray-200">
                    <p className="text-sm text-gray-500 mb-1">Actual Engagement</p>
                    <p className="text-gray-700">{beforeAfter.before.engagement}</p>
                  </div>
                </div>
              </Card>
            </motion.div>

            {/* Arrow */}
            <div className="flex justify-center lg:justify-start">
              <motion.div
                initial={{ opacity: 0, scale: 0.5 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
                className="w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center shadow-lg"
              >
                <ArrowRight className="w-8 h-8 text-white" />
              </motion.div>
            </div>

            {/* After */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="p-8 border-2 border-green-100 bg-green-50/30">
                <div className="flex items-center justify-between mb-6">
                  <Badge className="bg-green-600 text-white">
                    After (HookForge)
                  </Badge>
                  <div className="flex items-center text-green-600">
                    <TrendingUp className="w-4 h-4 mr-1" />
                    <span className="text-sm font-medium">High Performance</span>
                  </div>
                </div>
                
                <blockquote className="text-lg text-gray-700 mb-6 font-medium">
                  "{beforeAfter.after.text}"
                </blockquote>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Viralness Score</span>
                    <div className="flex items-center">
                      <div className="w-20 h-2 bg-gray-200 rounded-full mr-3">
                        <div 
                          className="h-2 bg-green-500 rounded-full transition-all duration-1000 delay-500"
                          style={{ width: `${beforeAfter.after.score}%` }}
                        ></div>
                      </div>
                      <span className="text-lg font-bold text-green-600">{beforeAfter.after.score}</span>
                    </div>
                  </div>
                  
                  <div className="bg-white p-3 rounded-lg border border-gray-200">
                    <p className="text-xs text-gray-500 mb-1">Hook Pattern Detected</p>
                    <p className="text-sm font-medium text-gray-700">{beforeAfter.after.pattern}</p>
                  </div>
                  
                  <div className="pt-4 border-t border-gray-200">
                    <p className="text-sm text-gray-500 mb-1">Projected Engagement</p>
                    <p className="text-gray-700 font-medium">{beforeAfter.after.engagement}</p>
                  </div>
                </div>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}