import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { cn } from '@/lib/utils';
import { Toaster } from '@/components/ui/sonner';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'HookForge - Your Next Viral Hook Starts Here',
  description: 'Analyze your past tweets, discover your hook DNA, and generate viral content with AI-powered insights.',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={cn(inter.className, 'antialiased')}>
        {children}
        <Toaster richColors position="bottom-right" />
      </body>
    </html>
  );
}