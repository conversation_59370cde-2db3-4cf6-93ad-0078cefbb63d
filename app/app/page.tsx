'use client';

import { useState } from 'react';
import { Header } from '@/components/layout/header';
import { CSVUpload } from '@/components/app/csv-upload';
import { HookDNAAnalysis } from '@/components/app/hook-dna-analysis';
import { HookGenerator } from '@/components/app/hook-generator';
import { HookBooster } from '@/components/app/hook-booster';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card } from '@/components/ui/card';

export default function AppPage() {
  const [tweetData, setTweetData] = useState<any[]>([]);
  const [hookDNA, setHookDNA] = useState<any>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const handleCSVUploaded = async (data: any[]) => {
    setTweetData(data);
    setIsAnalyzing(true);
    
    try {
      const response = await fetch('/api/analyze-hooks', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ tweets: data }),
      });
      
      const analysis = await response.json();
      setHookDNA(analysis);
    } catch (error) {
      console.error('Analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            HookForge Studio
          </h1>
          <p className="text-lg text-gray-600">
            Transform your content creation with AI-powered hook analysis and generation.
          </p>
        </div>

        <Tabs defaultValue="upload" className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-8">
            <TabsTrigger value="upload">Upload Data</TabsTrigger>
            <TabsTrigger value="dna" disabled={!hookDNA}>Hook DNA</TabsTrigger>
            <TabsTrigger value="generate" disabled={!hookDNA}>Generate</TabsTrigger>
            <TabsTrigger value="boost" disabled={!hookDNA}>Boost Hooks</TabsTrigger>
          </TabsList>

          <TabsContent value="upload">
            <Card className="p-8">
              <CSVUpload 
                onUpload={handleCSVUploaded} 
                isAnalyzing={isAnalyzing}
              />
            </Card>
          </TabsContent>

          <TabsContent value="dna">
            <HookDNAAnalysis data={hookDNA} />
          </TabsContent>

          <TabsContent value="generate">
            <HookGenerator hookDNA={hookDNA} />
          </TabsContent>

          <TabsContent value="boost">
            <HookBooster hookDNA={hookDNA} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}